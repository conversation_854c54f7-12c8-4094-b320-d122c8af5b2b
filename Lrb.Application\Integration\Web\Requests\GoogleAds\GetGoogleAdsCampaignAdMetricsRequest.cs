﻿using Google.Ads.GoogleAds;
using Google.Ads.GoogleAds.Config;
using Google.Ads.GoogleAds.Lib;
using Google.Ads.GoogleAds.V20.Services;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.Integration;
using Microsoft.AspNetCore.Http;
using static Lrb.Application.Integration.Web.Requests.GoogleAds.GoogleCampaignAdMetricsDto;

namespace Lrb.Application.Integration.Web.Requests.GoogleAds
{
    public class GetGoogleAdsCampaignAdMetricsRequest : IRequest<Response<List<GoogleCampaignAdMetricsDto>>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public Guid AccountId { get; set; } = new();
        public List<string>? CampaignIds { get; set; } = new();
        public string? TimeZoneId { get; set; }
        public TimeSpan? BaseUTcOffset { get; set; }
    }
    public class GetGoogleAdsCampaignAdMetricsRequestHandler : IRequestHandler<GetGoogleAdsCampaignAdMetricsRequest, Response<List<GoogleCampaignAdMetricsDto>>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<GoogleAdsInfo> _googleAdsInfos;
        private readonly IGoogleAdsService _googleAdsService;
        private readonly IRepositoryWithEvents<GoogleAdsAuthResponse> _googleAdsAuthResponse;
        public GetGoogleAdsCampaignAdMetricsRequestHandler(
            ICurrentUser currentUser,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<GoogleAdsInfo> googleAdsInfos,
            IGoogleAdsService googleAdsService,
            IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponse
            )
        {
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
            _googleAdsInfos = googleAdsInfos;
            _googleAdsService = googleAdsService;
            _googleAdsAuthResponse = googleAdsAuthResponse;
        }

        public async Task<Response<List<GoogleCampaignAdMetricsDto>>> Handle(GetGoogleAdsCampaignAdMetricsRequest request, CancellationToken cancellationToken)
        {
            var authResponse = await _googleAdsAuthResponse.FirstOrDefaultAsync(new GetGoogleAdsAuthResponseByIdSpec(request?.AccountId ?? Guid.Empty));
            if (authResponse == null)
            {
                throw new NotFoundException("No Google Ads account found for this user.");
            }
            var config = new GoogleAdsConfig()
            {
                DeveloperToken = _googleAdsService.DeveloperToken,
                OAuth2ClientId = _googleAdsService.ClientId,
                OAuth2ClientSecret = _googleAdsService.ClientSecret,
                OAuth2RefreshToken = authResponse.RefreshToken,
                LoginCustomerId = authResponse.CustomerId
            };
            var tenantId = _currentUser.GetTenant();
            TimeSpan timeZoneOffset = request.BaseUTcOffset ?? TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow);
            GoogleAdsClient client = new(config);
            var service = client.GetService(Services.V20.GoogleAdsService);
            var revenueResults = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<GoogleAdRevenueResult>(
                "LeadratBlack", "GetGoogleAdsAdRevenueByCampaigns",
                new { p_tenantid = tenantId, p_campaignids = request.CampaignIds?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()), })).ToList();
            var filteredCampaignIds = revenueResults.Select(r => r.CampaignId).Distinct().ToList();
            var customerIds = await _googleAdsInfos.ListAsync(new GetCustomerIdsByCampaignsSpecs(filteredCampaignIds ?? null));
            var distinctCustomerIds = customerIds.Where(x => x.CustomerId != null).Select(x => x.CustomerId.ToString()).Distinct().ToList();
            var from = ((request.FromDate ?? DateTime.UtcNow.Date) + timeZoneOffset).ToString("yyyy-MM-dd");
            var to = ((request.ToDate ?? DateTime.UtcNow.Date) + timeZoneOffset).ToString("yyyy-MM-dd");
            string campaignFilter = (request.CampaignIds != null && request.CampaignIds.Any()) ? $"AND campaign.id IN ({string.Join(",", request.CampaignIds.Select(id => id.Trim()))})" : "";
            string query = $@"
                    SELECT
                      campaign.id,
                      campaign.name,
                      metrics.cost_micros,
                      metrics.conversions,
                      campaign.status,
                      metrics.conversions_value,
                      metrics.clicks,
                      metrics.average_cpc
                    FROM campaign
                    WHERE
                    segments.date BETWEEN '{from}' AND '{to}'
                    {campaignFilter}";
            var metricsList = new List<GoogleCampaignAdMetricsDto>();
            foreach (var customerId in distinctCustomerIds)
            {
                var searchRequest = new SearchGoogleAdsRequest
                {
                    CustomerId = customerId,
                    Query = query
                };

                var response = service.SearchAsync(searchRequest);
                await foreach (var row in response)
                {
                    decimal spend = row.Metrics?.CostMicros != null ? row.Metrics.CostMicros / 1_000_000m : 0m;
                    double leads = row.Metrics?.Conversions ?? 0;
                    double? cpl = leads > 0 ? (double)spend / leads : null;
                    var revenue = revenueResults.FirstOrDefault(r => r.CampaignId == row.Campaign?.Id.ToString())?.TotalRevenue ?? 0;
                    var investment = row.Metrics?.CostMicros / 1_000_000m ?? 0;
                    decimal? roi = null;
                    if (revenue > 0 && investment > 0)
                    {
                        roi = Math.Round(((revenue - investment) / investment) * 100m, 2);
                    }
                    var dto = new GoogleCampaignAdMetricsDto
                    {
                        CampaignId = row.Campaign?.Id ?? 0,
                        CampaignName = row.Campaign?.Name ?? "N/A",
                        AdId = row.AdGroupAd?.Ad?.Id ?? 0,
                        Budget = row.CampaignBudget?.AmountMicros != null ? row.CampaignBudget.AmountMicros / 1_000_000m : 0m,
                        Cost = row.Metrics?.CostMicros != null ? row.Metrics.CostMicros / 1_000_000m : 0m,
                        LeadCount = row.Metrics?.Conversions ?? 0,
                        ConversionsValue = row.Metrics?.ConversionsValue ?? 0,
                        Clicks = row.Metrics?.Clicks ?? 0,
                        AverageCpc = row.Metrics?.AverageCpc != null ? row.Metrics.AverageCpc / 1_000_000 : 0,
                        CostPerLead = cpl.HasValue ? Math.Round((decimal)cpl.Value, 2) : null,
                        TotalRevenue = revenue,
                        RoiPercentage = roi > 0 ? Math.Round(roi.Value, 2) : 0,
                        Status = row.Campaign?.Status.ToString() ?? "N/A",
                    };

                    metricsList.Add(dto);
                }
            }
            return new(metricsList.ToList());
        }
    }
    
}

﻿using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.Stores;
using Hangfire;
using Hangfire.Server;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Lead.Web;
using Lrb.Application.LeadRotation.Web.Dtos;
using Lrb.Application.Notifications.Dtos;
using Lrb.Application.PushNotification.Web.Dtos;
using Lrb.Application.SMS.Mobile;
using Lrb.Application.WhatsAppCloudApi.Web;
using Lrb.Infrastructure.Auth;
using Lrb.Infrastructure.Common;
using Lrb.Infrastructure.Multitenancy;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace Lrb.Infrastructure.BackgroundJobs;

public class LrbJobActivator : JobActivator
{
    private readonly IServiceScopeFactory _scopeFactory;

    public LrbJobActivator(IServiceScopeFactory scopeFactory)
    {
        _scopeFactory = scopeFactory ?? throw new ArgumentNullException(nameof(scopeFactory));
    }
    public override JobActivatorScope BeginScope(PerformContext context) =>
        new Scope(context, _scopeFactory.CreateScope());

    private class Scope : JobActivatorScope, IServiceProvider
    {
        private readonly PerformContext _context;
        private readonly IServiceScope _scope;
        private readonly IMultiTenantStore<LrbTenantInfo> _inMemoryStore;



        public Scope(PerformContext context, IServiceScope scope)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _scope = scope ?? throw new ArgumentNullException(nameof(scope));
            _inMemoryStore = _scope.ServiceProvider.GetRequiredService<IMultiTenantStore<LrbTenantInfo>>();
            string tenantId = string.Empty;
            Guid currentuserId = Guid.Empty;
            List<Guid> currentuserIds = new List<Guid>();
            LrbTenantInfo tenantInfo = new LrbTenantInfo();
            if (context.BackgroundJob.Job.Args.Any() && (context.BackgroundJob.Job.Args[0]?.GetType()?.Equals(typeof(NotificationDTO)) ?? false))
            {
                var dto = (NotificationDTO)_context.BackgroundJob.Job.Args[0];
                LrbTenantInfoDto? tenantInfoDto = dto?.TenantInfoDto ?? null;
                tenantId = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                var cachedTenant = _inMemoryStore.TryGetAsync(tenantId);
                tenantInfo.Id = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                tenantInfo.Name = cachedTenant?.Result?.Name ?? string.Empty;
                tenantInfo.ConnectionString = cachedTenant?.Result?.ConnectionString ?? string.Empty;
                tenantInfo.Identifier = cachedTenant?.Result?.Identifier ?? string.Empty;
                currentuserId = dto.CurrentUserId;
            }
            if (context.BackgroundJob.Job.Args.Any() && (context.BackgroundJob.Job.Args[0]?.GetType()?.Equals(typeof(EmailSenderDto)) ?? false))
            {
                var dto = (EmailSenderDto)_context.BackgroundJob.Job.Args[0];
                LrbTenantInfoDto? tenantInfoDto = dto?.TenantInfoDto ?? null;
                tenantId = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                var cachedTenant = _inMemoryStore.TryGetAsync(tenantId);
                tenantInfo.Id = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                tenantInfo.Name = cachedTenant?.Result?.Name ?? string.Empty;
                tenantInfo.ConnectionString = cachedTenant?.Result?.ConnectionString ?? string.Empty;
                tenantInfo.Identifier = cachedTenant?.Result?.Identifier ?? string.Empty;
                currentuserId = dto.CurrentUserId;
            }
            if (context.BackgroundJob.Job.Args.Any() && (context.BackgroundJob.Job.Args[0]?.GetType()?.Equals(typeof(TemplateDto)) ?? false))
            {
                var dto = (TemplateDto)_context.BackgroundJob.Job.Args[0];
                LrbTenantInfoDto? tenantInfoDto = dto?.TenantInfoDto ?? null;
                tenantId = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                var cachedTenant = _inMemoryStore.TryGetAsync(tenantId);
                tenantInfo.Id = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                tenantInfo.Name = cachedTenant?.Result?.Name ?? string.Empty;
                tenantInfo.ConnectionString = cachedTenant?.Result?.ConnectionString ?? string.Empty;
                tenantInfo.Identifier = cachedTenant?.Result?.Identifier ?? string.Empty;
                currentuserId = dto.CurrentUserId;
            }
            if (context.BackgroundJob.Job.Args.Any() && (context.BackgroundJob.Job.Args[0]?.GetType()?.Equals(typeof(FacebookIntegrationDto)) ?? false))
            {
                var dto = (FacebookIntegrationDto)_context.BackgroundJob.Job.Args[0];
                LrbTenantInfoDto? tenantInfoDto = dto?.TenantInfoDto ?? null;
                tenantId = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                tenantInfo.Id = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                tenantInfo.Name = tenantInfoDto?.Name ?? string.Empty;
                tenantInfo.ConnectionString = tenantInfoDto?.ConnectionString ?? string.Empty;
                tenantInfo.Identifier = tenantInfo.Identifier;
                currentuserId = dto.CurrentUserId;
            }
            if (context.BackgroundJob.Job.Args.Any() && (context.BackgroundJob.Job.Args[0]?.GetType()?.Equals(typeof(BulkUploadbackgroundDto)) ?? false))
            {
                var dto = (BulkUploadbackgroundDto)_context.BackgroundJob.Job.Args[0];
                LrbTenantInfoDto? tenantInfoDto = dto?.TenantInfoDto ?? null;
                tenantId = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                tenantInfo.Id = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                tenantInfo.Name = tenantInfoDto?.Name ?? string.Empty;
                tenantInfo.ConnectionString = tenantInfoDto?.ConnectionString ?? string.Empty;
                tenantInfo.Identifier = tenantInfo.Identifier;
                currentuserId = dto.CurrentUserId;
            }
            if (context.BackgroundJob.Job.Args.Any() && (context.BackgroundJob.Job.Args[0]?.GetType()?.Equals(typeof(BaseWhatsAppTemplateWithLeadIdDto)) ?? false))
            {
                var dto = (BaseWhatsAppTemplateWithLeadIdDto)_context.BackgroundJob.Job.Args[0];
                LrbTenantInfoDto? tenantInfoDto = dto?.TenantInfoDto ?? null;
                tenantId = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                var cachedTenant = _inMemoryStore.TryGetAsync(tenantId);
                tenantInfo.Id = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                tenantInfo.Name = cachedTenant?.Result?.Name ?? string.Empty;
                tenantInfo.ConnectionString = cachedTenant?.Result?.ConnectionString ?? string.Empty;
                tenantInfo.Identifier = cachedTenant?.Result?.Identifier ?? string.Empty;
                currentuserId = dto?.CurrentUserId ?? Guid.Empty;
            }
            if (context.BackgroundJob.Job.Args.Any() && (context.BackgroundJob.Job.Args[0]?.GetType()?.Equals(typeof(LeadsAssignRotationInfoDto)) ?? false))
            {
                var dto = (LeadsAssignRotationInfoDto)_context.BackgroundJob.Job.Args[0];
                LrbTenantInfoDto? tenantInfoDto = dto?.TenantInfoDto ?? null;
                tenantId = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                tenantInfo.Id = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                tenantInfo.Name = tenantInfoDto?.Name ?? string.Empty;
                tenantInfo.ConnectionString = tenantInfoDto?.ConnectionString ?? string.Empty;
                tenantInfo.Identifier = tenantInfo.Identifier;
                currentuserId = dto?.CurrentUserId ?? Guid.Empty;
            }
            if (context.BackgroundJob.Job.Args.Any() && (context.BackgroundJob.Job.Args[0]?.GetType()?.Equals(typeof(NotificationInfoDto)) ?? false))
            {
                var dto = (NotificationInfoDto)_context.BackgroundJob.Job.Args[0];
                LrbTenantInfoDto? tenantInfoDto = dto?.TenantInfoDto ?? null;
                tenantId = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                var cachedTenant = _inMemoryStore.TryGetAsync(tenantId);
                tenantInfo.Id = tenantInfoDto?.Id?.ToString() ?? string.Empty;
                tenantInfo.Name = cachedTenant?.Result?.Name ?? string.Empty;
                tenantInfo.ConnectionString = cachedTenant?.Result?.ConnectionString ?? string.Empty;
                tenantInfo.Identifier = cachedTenant?.Result?.Identifier ?? string.Empty;
                currentuserIds = dto?.UserIds ?? new List<Guid>();
            }
            ReceiveParameters(tenantInfo, currentuserId);
        }

        private void ReceiveParameters(LrbTenantInfo tenantInfo, Guid? currentuserId = null)
        {

            // var tenantInfo = _context.GetJobParameter<LrbTenantInfo>(MultitenancyConstants.TenantIdName);
            if (tenantInfo is not null)
            {
                _scope.ServiceProvider.GetRequiredService<IMultiTenantContextAccessor>()
                    .MultiTenantContext = new MultiTenantContext<LrbTenantInfo>
                    {
                        TenantInfo = tenantInfo
                    };
            }

            string userId = _context.GetJobParameter<string>(QueryStringKeys.UserId) ?? currentuserId?.ToString();
            if (!string.IsNullOrEmpty(userId))
            {
                _scope.ServiceProvider.GetRequiredService<ICurrentUserInitializer>()
                    .SetCurrentUserId(userId);
            }
        }

        public override object Resolve(Type type)
        {
            try
            {
                return ActivatorUtilities.GetServiceOrCreateInstance(this, type);

            }catch(Exception e)
            {
                try
                {
                   var logger =  _scope.ServiceProvider.GetRequiredService<Serilog.ILogger>();
                    logger.Information("LrbJobActivator ->  Scope -> Resolve(): Exception " + JsonConvert.SerializeObject(e, new JsonSerializerSettings() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore}));
                }
                catch { }
            }
            return null;

        }

        object? IServiceProvider.GetService(Type serviceType) =>
            serviceType == typeof(PerformContext)
                ? _context
                : _scope.ServiceProvider.GetService(serviceType);
    }
}
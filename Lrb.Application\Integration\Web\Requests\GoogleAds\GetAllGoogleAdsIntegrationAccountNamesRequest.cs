﻿using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Requests.GoogleAds
{
    public class GetAllGoogleAdsIntegrationAccountNamesRequest : PaginationFilter, IRequest<PagedResponse<GoogleAdsNamesAuthResponseDto, string>>
    {
        public LeadSource? LeadSource { get; set; }
    }
    public class GetAllGoogleAdsIntegrationAccountNamesRequestHandler : IRequestHandler<GetAllGoogleAdsIntegrationAccountNamesRequest, PagedResponse<GoogleAdsNamesAuthResponseDto, string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationRepo;
        private readonly IRepositoryWithEvents<GoogleAdsAuthResponse> _googleAdsAuthResponseRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetAllGoogleAdsIntegrationAccountNamesRequestHandler(IRepositoryWithEvents<IntegrationAccountInfo> integrationRepo, IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _integrationRepo = integrationRepo;
            _googleAdsAuthResponseRepo = googleAdsAuthResponseRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<GoogleAdsNamesAuthResponseDto, string>> Handle(GetAllGoogleAdsIntegrationAccountNamesRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant() ?? string.Empty;
            var googleAccounts = await _dapperRepository.GetGoogleAdsAccountsAsync(request.LeadSource ?? LeadSource.GoogleAdsCampaign, tenantId);
            if (googleAccounts?.Any() == true)
            {
                var count = await _integrationRepo.CountAsync(new GoogleAdsAccountsSpec(request.LeadSource ?? LeadSource.GoogleAdsCampaign), cancellationToken);
                var dtos = googleAccounts.Adapt<List<GoogleAdsNamesAuthResponseDto>>();
                return new PagedResponse<GoogleAdsNamesAuthResponseDto, string>(dtos.Adapt<List<GoogleAdsNamesAuthResponseDto>>(), count);
            }
            return new PagedResponse<GoogleAdsNamesAuthResponseDto, string>(new List<GoogleAdsNamesAuthResponseDto>(), 0);
        }

    }
}

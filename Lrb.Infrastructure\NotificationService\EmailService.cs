﻿using Finbuckle.MultiTenant;
using Lrb.Application.Common.Email;
using Lrb.Application.Common.GraphEmail;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.NotificationService;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Auth;
using Mapster;
using Newtonsoft.Json;
using System.Reflection;

namespace Lrb.Infrastructure.NotificationService
{
    public class EmailService : IEmailService
    {
        private readonly IRepositoryWithEvents<EmailSettings> _emailSettingsRepo;
        private readonly IRepositoryWithEvents<EmailTemplates> _emailTemplatesRepo;
        private readonly IRepositoryWithEvents<EmailServiceProvider> _emailServiceProviderRepo;
        private readonly IRepositoryWithEvents<EmailConfigurationData> _emailConfigurationDataRepo;
        private readonly IRepositoryWithEvents<EmailApiIntegrationData> _emailApiIntegrationDataRepo;
        private readonly IRepositoryWithEvents<MasterEmailServiceProvider> _masterEmailServiceProviderRepo;
        private readonly IGraphEmailService _graphEmailService;
        private readonly IUserService _userService;
        private readonly IJobService _hangfireService;
        private readonly IReadRepository<UserDetails> _userDetailsRepo;
        private readonly IRepositoryWithEvents<NotificationServiceTracker> _notificationServiceTrackerRepo;
        private readonly ITenantInfo _tenantInfo;
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;

        public EmailService(IRepositoryWithEvents<EmailApiIntegrationData> emailApiIntegrationData,
                IGraphEmailService graphEmailService,
            IUserService userService,
            IJobService hangfireService,
            IReadRepository<UserDetails> userDetailsRepo,
            IRepositoryWithEvents<MasterEmailServiceProvider> masterEmailServiceProviderRepo,
            IRepositoryWithEvents<EmailSettings> emailSettingsRepo,
            IRepositoryWithEvents<EmailTemplates> emailTemplatesRepo,
            IRepositoryWithEvents<EmailServiceProvider> emailServiceProvider,
            IRepositoryWithEvents<EmailConfigurationData> emailConfigurationData,
            IRepositoryWithEvents<NotificationServiceTracker> notificationServiceTrackerRepo,
            ITenantInfo tenantInfo,
            ICurrentUser currentUser,
            ILeadRepositoryAsync leadRepositoryAsync)
        {
            _emailSettingsRepo = emailSettingsRepo;
            _emailTemplatesRepo = emailTemplatesRepo;
            _emailServiceProviderRepo = emailServiceProvider;
            _emailConfigurationDataRepo = emailConfigurationData;
            _emailApiIntegrationDataRepo = emailApiIntegrationData;
            _graphEmailService = graphEmailService;
            _userDetailsRepo = userDetailsRepo;
            _masterEmailServiceProviderRepo = masterEmailServiceProviderRepo;
            _notificationServiceTrackerRepo = notificationServiceTrackerRepo;
            _userService = userService;
            _hangfireService = hangfireService;
            _tenantInfo = tenantInfo;
            _currentUser = currentUser;
            _leadRepositoryAsync = leadRepositoryAsync;
        }


        public async Task<bool> SendEmailByLrServiceAsync<T>(T entity, List<MasterEmailTemplates> emailTemplates, Event @event, string userName)
        {
            var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetMasterEmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
            string? senderEmailService = serviceProvider?.SenderEmailAddress ?? null;
            List<EmailSenderDto> emailSenderDtos = new List<EmailSenderDto>();
            DateTime scheduledDateTime = DateTime.UtcNow;
            PropertyInfo[] properties = typeof(T).GetProperties();
            string? entityId = properties?.FirstOrDefault(i => i?.Name == "Id")?.GetValue(entity)?.ToString() ?? string.Empty;
            string? type = typeof(T).FullName ?? null;
            if (type != null)
            {
                if (type == typeof(Domain.Entities.Lead).FullName)
                {
                    string? userId = properties?.FirstOrDefault(i => i?.Name == "AssignTo")?.GetValue(entity)?.ToString() ?? string.Empty;
                    var leadEmail = properties?.FirstOrDefault(i => i?.Name == "Email")?.GetValue(entity)?.ToString() ?? string.Empty;
                    scheduledDateTime = (DateTime)(properties?.FirstOrDefault(i => i?.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow);
                    var leadName = properties?.FirstOrDefault(i => i?.Name == "Name")?.GetValue(entity)?.ToString() ?? string.Empty;
                    leadName = leadName.Split(' ')[0];
                    var userDetails = await _userService.GetAsync(userId, CancellationToken.None);
                    var userEmail = userDetails.Email;
                    userName = userDetails?.FirstName ?? "User";
                    var leadTemplate = emailTemplates.Where(i => i.IsleadSpecific).FirstOrDefault();
                    var userTemplate = emailTemplates.Where(i => !i.IsleadSpecific).FirstOrDefault();
                    if (leadTemplate != null)
                    {
                        var leadEmailDto = new EmailSenderDto()
                        {
                            ScheduledDate = scheduledDateTime,
                            SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty,
                            To = new List<string>() { leadEmail },
                            Cc = new List<string>() { },
                            Bcc = new List<string>() { },
                            EmailBody = leadTemplate.Body,
                            Subject = leadTemplate?.Subject ?? string.Empty,
                            FileAttachments = new List<string>() { }
                        };
                        emailSenderDtos.Add(leadEmailDto);
                    }
                    if (userTemplate != null)
                    {
                        var userEmailDto = new EmailSenderDto()
                        {
                            ScheduledDate = scheduledDateTime,
                            SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty,
                            To = new List<string>() { userEmail },
                            Cc = new List<string>() { },
                            Bcc = new List<string>() { },
                            EmailBody = userTemplate?.Body ?? string.Empty,
                            Subject = userTemplate?.Subject ?? string.Empty,
                            FileAttachments = new List<string>() { }
                        };
                        emailSenderDtos.Add(userEmailDto);
                    }
                }
                else if (type == typeof(Domain.Entities.Todo).FullName)
                {
                    scheduledDateTime = (DateTime)(properties?.FirstOrDefault(i => i?.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow);
                    string? userId = properties?.FirstOrDefault(i => i?.Name == "AssignedToUserId")?.GetValue(entity)?.ToString() ?? string.Empty;
                    var userDetails = await _userService.GetAsync(userId, CancellationToken.None);
                    var userEmail = userDetails.Email;
                    userName = userDetails?.FirstName ?? "User";
                    var userTemplate = emailTemplates.FirstOrDefault();
                    var userEmailDto = new EmailSenderDto()
                    {
                        ScheduledDate = scheduledDateTime,
                        SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty,
                        To = new List<string>() { userEmail },
                        Cc = new List<string>() { },
                        Bcc = new List<string>() { },
                        EmailBody = userTemplate?.Body ?? string.Empty,
                        Subject = userTemplate?.Subject ?? string.Empty,
                        FileAttachments = new List<string>() { }
                    };
                    emailSenderDtos.Add(userEmailDto);
                }
                else
                {
                    scheduledDateTime = (DateTime)(properties?.FirstOrDefault(i => i?.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow);
                    var userEmail = properties?.FirstOrDefault(i => i?.Name == "Email")?.GetValue(entity)?.ToString() ?? string.Empty;
                    userName = properties?.FirstOrDefault(i => i?.Name == "Name")?.GetValue(entity)?.ToString() ?? string.Empty;
                    if (string.IsNullOrEmpty(userName))
                    {
                        userName = properties?.FirstOrDefault(i => i?.Name == "FirstName")?.GetValue(entity)?.ToString() ?? string.Empty;
                    }
                    var userTemplate = emailTemplates.FirstOrDefault();
                    var userEmailDto = new EmailSenderDto()
                    {
                        ScheduledDate = scheduledDateTime,
                        SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty,
                        To = new List<string>() { userEmail },
                        Cc = new List<string>() { },
                        Bcc = new List<string>() { },
                        EmailBody = userTemplate?.Body ?? string.Empty,
                        Subject = userTemplate?.Subject ?? string.Empty,
                        FileAttachments = new List<string>() { }
                    };
                    emailSenderDtos.Add(userEmailDto);
                }
            }
            var updatedEmailSenderDtos = GetEmailTemplates(entity, emailSenderDtos, @event);
            bool result = await SendAsync(updatedEmailSenderDtos, serviceProvider.ServiceProvider, entityId, @event, scheduledDateTime);
            return result;
        }

        public List<EmailSenderDto> GetEmailTemplates<T>(T entity, List<EmailSenderDto> dtos, Event @event)
        {
            var scheduledDate = typeof(T).FullName == typeof(Todo).FullName
                    ? (DateTime)(typeof(T).GetProperty("ScheduledDateTime").GetValue(entity) ?? DateTime.UtcNow)
                    : typeof(T).FullName == typeof(Lead).FullName
                                ? (DateTime)(typeof(T).GetProperty("ScheduledDate").GetValue(entity) ?? DateTime.UtcNow)
                                : DateTime.UtcNow;
            List<EmailSenderDto> newDtos = new();
            foreach (var dto in dtos)
            {
                switch (@event)
                {
                    case Event.CallbackReminder:
                    case Event.ScheduleSiteVisitReminder:
                    case Event.ScheduleMeetingReminder:
                    case Event.ScheduledTaskReminder:
                        dto.IsScheduled = true;
                        List<int> minutesBeforeList = new() { 30, 15 };
                        foreach (var minutes in minutesBeforeList)
                        {
                            EmailSenderDto newDto = new();
                            if (scheduledDate.AddMinutes(-minutes) < DateTime.UtcNow)
                            {
                                dto.IsScheduled = false;
                                newDto = dto.Adapt(newDto);
                                newDto.EmailBody = newDto.EmailBody.Replace(NotificationVariables.MinutesBefore, minutes.ToString());
                                newDtos.Add(newDto);
                            }
                            else
                            {
                                dto.ScheduledDate = scheduledDate.AddMinutes(-minutes);
                                newDto = dto.Adapt(newDto);
                                newDto.EmailBody = newDto.EmailBody.Replace(NotificationVariables.MinutesBefore, minutes.ToString());
                                newDtos.Add(newDto);
                            }
                        }
                        break;
                    default:
                        newDtos.Add(dto);
                        break;
                }
            }
            return newDtos;
        }

        public async Task<bool> SendAsync(List<EmailSenderDto> emailSenderDtos, EmailServiceProviders? emailServiceProvider, string entityId, Event @event, DateTime scheduledDateTime)
        {
            Guid? currentUserId = _currentUser.GetUserId();
            List<NotificationServiceTracker> trackers = new();
            try
            {
                if (emailServiceProvider == EmailServiceProviders.GraphEmail)
                {
                    foreach (var dto in emailSenderDtos)
                    {
                        if (dto != null && !string.IsNullOrEmpty(dto.SenderEmailAddress) && !string.IsNullOrEmpty(dto.Subject) && !string.IsNullOrEmpty(dto.EmailBody) && (dto.To != null && dto.To.Count > 0))
                        {
                            dto.CurrentUserId = _currentUser.GetUserId();
                            dto.TenantInfoDto = new()
                            {
                                Id = _tenantInfo.Id
                            };
                            if (!dto.IsScheduled)
                            {
                                await _graphEmailService.SendEmail(dto);
                            }
                            else
                            {
                                var jobId = _hangfireService.Schedule(() => _graphEmailService.SendEmail(dto), dto.ScheduledDate.ToLocalTime());
                                NotificationServiceTracker tracker = new();
                                tracker.JobId = jobId;
                                tracker.EntityId = entityId == null ? Guid.Empty : Guid.Parse(entityId);
                                tracker.Event = @event;
                                tracker.ScheduledDate = scheduledDateTime;
                                trackers.Add(tracker);
                            }
                        }
                    }
                    foreach (var tracker in trackers)
                    {
                        await _notificationServiceTrackerRepo.AddAsync(tracker, CancellationToken.None);
                    }
                }
            }
            catch (Exception ex) 
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "EmailService -> SendAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return true;
        }
        public async Task<bool> SendEmailByThirdPartyAsync<T>(T entity, List<EmailTemplates> emailTemplates, Event @event, string userName, EmailServiceProviders currentServiceProvider)
        {
            var serviceProvider = (await _emailServiceProviderRepo.ListAsync(new GetEmailServiceProviderSpec(currentServiceProvider), CancellationToken.None)).FirstOrDefault();
            string? senderEmailService = serviceProvider?.SenderEmailAddress ?? null;
            List<EmailSenderDto> emailSenderDtos = new List<EmailSenderDto>();
            DateTime scheduledDateTime = DateTime.UtcNow;
            PropertyInfo[] properties = typeof(T).GetProperties();
            string? entityId = properties?.FirstOrDefault(i => i?.Name == "Id")?.GetValue(entity)?.ToString() ?? string.Empty;
            string? type = typeof(T).FullName ?? null;
            if (type != null)
            {
                if (type == typeof(Domain.Entities.Lead).FullName)
                {
                    string? userId = properties?.FirstOrDefault(i => i?.Name == "AssignTo")?.GetValue(entity)?.ToString() ?? string.Empty;
                    var leadEmail = properties?.FirstOrDefault(i => i?.Name == "Email")?.GetValue(entity)?.ToString() ?? string.Empty;
                    scheduledDateTime = (DateTime)(properties?.FirstOrDefault(i => i?.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow);
                    var leadName = properties?.FirstOrDefault(i => i?.Name == "Name")?.GetValue(entity)?.ToString() ?? string.Empty;
                    leadName = leadName.Split(' ')[0];
                    var userDetails = await _userService.GetAsync(userId, CancellationToken.None);
                    var userEmail = userDetails.Email;
                    userName = userDetails?.FirstName ?? "User";
                    var leadTemplate = emailTemplates.Where(i => i.IsLeadSpecific).FirstOrDefault();
                    var userTemplate = emailTemplates.Where(i => !i.IsLeadSpecific).FirstOrDefault();
                    var leadEmailDto = new EmailSenderDto()
                    {
                        ScheduledDate = scheduledDateTime,
                        SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty,
                        To = new List<string>() { leadEmail },
                        Cc = new List<string>() { },
                        Bcc = new List<string>() { },
                        EmailBody = leadTemplate.Body,
                        Subject = leadTemplate?.Subject ?? string.Empty,
                        FileAttachments = new List<string>() { }
                    };
                    emailSenderDtos.Add(leadEmailDto);
                    var userEmailDto = new EmailSenderDto()
                    {
                        ScheduledDate = scheduledDateTime,
                        SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty,
                        To = new List<string>() { userEmail },
                        Cc = new List<string>() { },
                        Bcc = new List<string>() { },
                        EmailBody = userTemplate?.Body ?? string.Empty,
                        Subject = userTemplate?.Subject ?? string.Empty,
                        FileAttachments = new List<string>() { }
                    };
                    emailSenderDtos.Add(userEmailDto);
                }
                else if (type == typeof(Domain.Entities.Todo).FullName)
                {
                    scheduledDateTime = (DateTime)(properties?.FirstOrDefault(i => i?.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow);
                    string? userId = properties?.FirstOrDefault(i => i?.Name == "AssignedToUserId")?.GetValue(entity)?.ToString() ?? string.Empty;
                    var userDetails = await _userService.GetAsync(userId, CancellationToken.None);
                    var userEmail = userDetails.Email;
                    userName = userDetails?.FirstName ?? "User";
                    var userTemplate = emailTemplates.FirstOrDefault();
                    var userEmailDto = new EmailSenderDto()
                    {
                        ScheduledDate = scheduledDateTime,
                        SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty,
                        To = new List<string>() { userEmail },
                        Cc = new List<string>() { },
                        Bcc = new List<string>() { },
                        EmailBody = userTemplate?.Body ?? string.Empty,
                        Subject = userTemplate?.Subject ?? string.Empty,
                        FileAttachments = new List<string>() { }
                    };
                    emailSenderDtos.Add(userEmailDto);
                }
                else
                {
                    scheduledDateTime = (DateTime)(properties?.FirstOrDefault(i => i?.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow);
                    var userEmail = properties?.FirstOrDefault(i => i?.Name == "Email")?.GetValue(entity)?.ToString() ?? string.Empty;
                    userName = properties?.FirstOrDefault(i => i?.Name == "Name")?.GetValue(entity)?.ToString() ?? string.Empty;
                    if (string.IsNullOrEmpty(userName))
                    {
                        userName = properties?.FirstOrDefault(i => i?.Name == "FirstName")?.GetValue(entity)?.ToString() ?? string.Empty;
                    }
                    var userTemplate = emailTemplates.FirstOrDefault();
                    var userEmailDto = new EmailSenderDto()
                    {
                        ScheduledDate = scheduledDateTime,
                        SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty,
                        To = new List<string>() { userEmail },
                        Cc = new List<string>() { },
                        Bcc = new List<string>() { },
                        EmailBody = userTemplate?.Body ?? string.Empty,
                        Subject = userTemplate?.Subject ?? string.Empty,
                        FileAttachments = new List<string>() { }
                    };
                    emailSenderDtos.Add(userEmailDto);
                }
            }
            var updatedEmailSenderDtos = GetEmailTemplates(entity, emailSenderDtos, @event);
            bool result = await SendAsync(updatedEmailSenderDtos, serviceProvider.ServiceProvider, entityId, @event, scheduledDateTime);
            return result;
        }

        public async Task SendEmailAsync(EmailSenderDto dto)
        {
            await _graphEmailService.SendEmail(dto);
        }
    }

}
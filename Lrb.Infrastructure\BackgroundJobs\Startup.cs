﻿using Hangfire;
using Hangfire.Console;
using Hangfire.Console.Extensions;
using Hangfire.Dashboard;
using Hangfire.PostgreSql;
using Lrb.Infrastructure.Common;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Serilog;
using System.Text;

namespace Lrb.Infrastructure.BackgroundJobs;

internal static class Startup
{
    private static readonly ILogger _logger = Log.ForContext(typeof(Startup));

    internal static IServiceCollection AddBackgroundJobs(this IServiceCollection services, IConfiguration config)
    {
        try
        {
            var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "prd";
            if (!env.Contains("dev", StringComparison.InvariantCultureIgnoreCase))
            {
                services.AddHangfireServer(options => config.GetSection("HangfireSettings:Server").Bind(options));

                services.AddHangfireConsoleExtensions();

                var storageSettings = config.GetSection("HangfireSettings:Storage").Get<HangfireStorageSettings>();

                if (string.IsNullOrEmpty(storageSettings.StorageProvider)) throw new Exception("Hangfire Storage Provider is not configured.");
                if (string.IsNullOrEmpty(storageSettings.ConnectionString)) throw new Exception("Hangfire Storage Provider ConnectionString is not configured.");
                _logger.Information($"Hangfire: Current Storage Provider : {storageSettings.StorageProvider}");
                _logger.Information("For more Hangfire storage, visit https://www.hangfire.io/extensions.html");

                services.AddTransient<JobActivator, LrbJobActivator>();

                services.AddHangfire((provider, hangfireConfig) => hangfireConfig
                    .UseDatabase(storageSettings.StorageProvider, storageSettings.ConnectionString, config)
                    .UseFilter(new LrbTenantIndependentJobFilter(provider))
                    .UseFilter(new LogJobFilter())
                    .UseConsole());
            }

        }
        catch (Exception ex)
        {
            _logger.Error("Error occurred in AddBackgroundJobs(): " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }));
        }

        return services;
    }

    private static IGlobalConfiguration UseDatabase(this IGlobalConfiguration hangfireConfig, string dbProvider, string connectionString, IConfiguration config)
    {
        return dbProvider.ToLowerInvariant() switch
        {
            DbProviderKeys.Npgsql =>
                hangfireConfig.UsePostgreSqlStorage(connectionString, config.GetSection("HangfireSettings:Storage:Options").Get<PostgreSqlStorageOptions>()),

            _ => throw new Exception($"Hangfire Storage Provider {dbProvider} is not supported.")
        };
    }

    internal static IApplicationBuilder UseHangfireDashboard(this IApplicationBuilder app, IConfiguration config)
    {
        var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "prd";

        if (!env.Contains("dev", StringComparison.InvariantCultureIgnoreCase))
        {
            var dashboardOptions = config.GetSection("HangfireSettings:Dashboard").Get<DashboardOptions>() ?? new DashboardOptions();
            var securitySection = config.GetSection("HangfireSettings:Security");
            var authType = securitySection["AuthType"]?.ToLower();
            if (authType == "ip")
            {
                var allowedIps = securitySection.GetSection("AllowedIPs").Get<string[]>() ?? Array.Empty<string>();
                var user = securitySection.GetSection("Credentials:User").Value;
                var pass = securitySection.GetSection("Credentials:Password").Value;

                dashboardOptions.Authorization = new[]
                {
                    new HangfireIPThenBasicAuthFilter(allowedIps, user, pass)
                };
            }

            else if (authType == "basic")
            {
                var user = securitySection.GetSection("Credentials:User").Value;
                var pass = securitySection.GetSection("Credentials:Password").Value;

                dashboardOptions.Authorization = new[]
                {
                    new HangfireBasicAuthFilter(user, pass)
                };
            }
            else
            {
                dashboardOptions.Authorization = Enumerable.Empty<IDashboardAuthorizationFilter>();
            }

            app.UseHangfireDashboard(config["HangfireSettings:Route"], dashboardOptions);
        }

        return app;
    }


    public class HangfireIPFilter : IDashboardAuthorizationFilter
    {
        private readonly HashSet<string> _allowedIps;

        public HangfireIPFilter(IEnumerable<string> allowedIps)
        {
            _allowedIps = new HashSet<string>(allowedIps);
        }

        public bool Authorize(DashboardContext context)
        {
            var remoteIp = context.GetHttpContext().Connection.RemoteIpAddress?.ToString();
            return remoteIp != null && _allowedIps.Contains(remoteIp);
        }
    }


    public class HangfireBasicAuthFilter : IDashboardAuthorizationFilter
    {
        private readonly string _user;
        private readonly string _pass;

        public HangfireBasicAuthFilter(string user, string pass)
        {
            _user = user;
            _pass = pass;
        }

        public bool Authorize(DashboardContext context)
        {
            var httpContext = context.GetHttpContext();
            var authHeader = httpContext.Request.Headers["Authorization"].ToString();

            if (string.IsNullOrWhiteSpace(authHeader) || !authHeader.StartsWith("Basic "))
            {
                httpContext.Response.Headers["WWW-Authenticate"] = "Basic realm=\"Hangfire Dashboard\"";
                httpContext.Response.StatusCode = 401;
                return false;
            }

            var encodedCredentials = authHeader.Substring("Basic ".Length).Trim();
            var decoded = Encoding.UTF8.GetString(Convert.FromBase64String(encodedCredentials));
            var parts = decoded.Split(':');

            if (parts.Length != 2 || parts[0] != _user || parts[1] != _pass)
            {
                httpContext.Response.Headers["WWW-Authenticate"] = "Basic realm=\"Hangfire Dashboard\"";
                httpContext.Response.StatusCode = 401;
                return false;
            }

            return true;
        }
    }


    public class HangfireIPThenBasicAuthFilter : IDashboardAuthorizationFilter
    {
        private readonly HashSet<string> _allowedIps;
        private readonly HangfireBasicAuthFilter _basicAuthFilter;

        public HangfireIPThenBasicAuthFilter(IEnumerable<string> allowedIps, string user, string pass)
        {
            _allowedIps = new HashSet<string>(allowedIps ?? Enumerable.Empty<string>());
            _basicAuthFilter = new HangfireBasicAuthFilter(user, pass);
        }

        public bool Authorize(DashboardContext context)
        {
            var httpContext = context.GetHttpContext();
            var remoteIp = httpContext.Connection.RemoteIpAddress?.ToString();

            // If IP is allowed, grant access without auth
            if (!string.IsNullOrWhiteSpace(remoteIp) && _allowedIps.Contains(remoteIp))
            {
                return true;
            }

            // Otherwise, fallback to Basic Auth
            return _basicAuthFilter.Authorize(context);
        }
    }



    //public static IServiceCollection RunRecurringJobs(this IServiceCollection services)
    //{
    //    var provider = services.BuildServiceProvider();
    //    var recurringJobsInitializer = provider.GetRequiredService<IReccuringJobsInitializer>();
    //    recurringJobsInitializer.SetLeadStatusToPending();
    //    recurringJobsInitializer.SendEveningStatusUpdates();
    //    recurringJobsInitializer.SendMorningStatusUpdates();
    //    return services;
    //}

}
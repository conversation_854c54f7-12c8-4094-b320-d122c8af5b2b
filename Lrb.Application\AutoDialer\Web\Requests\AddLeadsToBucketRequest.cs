﻿using Lrb.Application.AutoDialer.Web.Dtos;
using Lrb.Application.AutoDialer.Web.Mappings;
using Lrb.Application.AutoDialer.Web.Specs;
using Lrb.Application.Common.IVR;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.UserDetails.Web;

namespace Lrb.Application.AutoDialer.Web.Requests
{
    public class AddLeadsToBucketRequest : IRequest<Response<bool>>
    {
        public List<AutoDialerAuditDto>? LeadDetails { get; set; }
        public string? TenantId { get; set; }
        public Guid? CurrentUserId { get; set; }
    }

    public class AddLeadsToBucketRequestHandler : IRequestHandler<AddLeadsToBucketRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<AutoDialerAudit> _autoDialerRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Domain.Entities.IVRCommonCallLog> _ivrCommonCallRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.IntegrationAccountInfo> _integrationAccRepo;
        private readonly IIVRService _iVRService;
        private readonly IServiceBus _serviceBus;
        private readonly IRepositoryWithEvents<AutoDialerConfiguration> _autoDialerConfigRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;

        public AddLeadsToBucketRequestHandler(IRepositoryWithEvents<AutoDialerAudit> autoDialerRepo, ICurrentUser currentUser,
            IIVRService iVRService, IRepositoryWithEvents<Domain.Entities.IntegrationAccountInfo> integrationAccRepo,
            IRepositoryWithEvents<Domain.Entities.IVRCommonCallLog> ivrCommonCallRepo, IServiceBus serviceBus, IRepositoryWithEvents<AutoDialerConfiguration> autoDialerConfigRepo,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo)
        {
            _autoDialerRepo = autoDialerRepo;
            _currentUser = currentUser;
            _iVRService = iVRService;
            _integrationAccRepo = integrationAccRepo;
            _ivrCommonCallRepo = ivrCommonCallRepo;
            _serviceBus = serviceBus;
            _autoDialerConfigRepo = autoDialerConfigRepo;
            _userDetailsRepo = userDetailsRepo;
        }

        public async Task<Response<bool>> Handle(AddLeadsToBucketRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var config = await _autoDialerConfigRepo.FirstOrDefaultAsync(new GetAutoDialerConfigSpec());
                if (config == null)
                    return new(false, "Configuration is not yet set");

                // Filter out invalid leads
                await FilterInvalidLeadsAsync(request.LeadDetails);

                var assigedUserIds = request.LeadDetails.Select(i => i.AssignTo).Distinct().ToList();

                // Assign order ranks and save audits
                await AddLeadsToAuditAsync(request);

                // Trigger call if available
                await TriggerCallIfAvailableAsync(request.TenantId, assigedUserIds ?? new(), config);
                bool hadInvalidLeads = request.LeadDetails?.Count < (request.LeadDetails?.Count ?? 0);
                return hadInvalidLeads
                    ? new(true, "Leads Added Successfully. Few leads were already in the bucket")
                    : new(true, "Leads Added Successfully");
            }
            catch (Exception ex)
            {
                return new(false, ex.Message);
            }
        }


        private async Task<List<AutoDialerAuditDto>> FilterInvalidLeadsAsync(List<AutoDialerAuditDto> leadDetails)
        {
            if (leadDetails == null || leadDetails.Count == 0)
                return new();

            var invalidLeadIds = (await _autoDialerRepo.ListAsync(
                new GetInvalidAutoDialerAuditSpec(leadDetails.Select(i => i.LeadId).ToList())))
                .Select(i => i.LeadId)
                .ToHashSet();

            return leadDetails
                .Where(i => !invalidLeadIds.Contains(i.LeadId))
                .ToList();
        }

        private async Task AddLeadsToAuditAsync(AddLeadsToBucketRequest request)
        {
            var topOrderAudit = await _autoDialerRepo.FirstOrDefaultAsync(
                new GetAutoDialerAuditByOrderRankSpec(request.CurrentUserId ?? _currentUser.GetUserId()))
                ?? new();

            var audits = request.LeadDetails?.Adapt<List<AutoDialerAudit>>() ?? new();
            int startOrderRank = (topOrderAudit.OrderRank ?? 0) + 1;

            for (int i = 0; i < audits.Count; i++)
            {
                audits[i].OrderRank = startOrderRank + i;
                audits[i].CallStatus = IVRCallStatus.InQueue;
            }
            await _autoDialerRepo.AddRangeAsync(audits);
        }

        private async Task TriggerCallIfAvailableAsync(string? tenantId, List<Guid?>? userIds, AutoDialerConfiguration config)
        {
            if (config.ShouldUseBGJob ?? false)
            {
                var payload = new InputPayloadAutoDailer(
                    tenantId ?? _currentUser.GetTenant(),
                    (userIds ?? new List<Guid?> { _currentUser.GetUserId() })
                        .Where(id => id.HasValue)
                        .Select(id => id.Value)
                        .ToList(),
                    DateTime.UtcNow,
                    config.MaxCallInterval ?? 0
                );

                await _serviceBus.RunAutoDialerTriggerJobAsync(payload);
            }
            foreach (var userId in userIds)
            {
                var isAvailableForCall = (await _userDetailsRepo.FirstOrDefaultAsync(
                    new GetUserDetailsByIdSpec(userId ?? _currentUser.GetUserId())))
                    ?.IsAvilableForCall ?? false;

                if (!isAvailableForCall) continue;

                await AutoDailerHelper.InitiateAutoCall(
                        userId ?? Guid.Empty, _autoDialerRepo, _integrationAccRepo, _ivrCommonCallRepo, _iVRService);
            }
        }

        public record InputPayloadAutoDailer(string TenantId, List<Guid> userIds, DateTime currentTimeWithInterval, int interval);

    }
}

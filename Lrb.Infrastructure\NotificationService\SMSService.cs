﻿using Finbuckle.MultiTenant;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.NotificationService;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.SMS;
using Lrb.Application.Notifications.Dtos;
using Lrb.Application.Sms.Mobile;
using Lrb.Application.SMS.Mobile;
using Lrb.Application.Utils;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using System.Reflection;

namespace Lrb.Infrastructure.NotificationService
{
    public class SMSService : ISMSService
    {
        private readonly IReadRepository<Domain.Entities.SMSService> _smsServiceRepo;
        private readonly IRepositoryWithEvents<MasterSMSQuota> _quotaRepo;
        private readonly IReadRepository<MasterSMSService> _masterSMSServiceRepo;
        private readonly ITenantInfo _currentTenant;
        private readonly ITextLocalSmsService _textLocalSmsService;
        private readonly IDakshInfoSoftSmsService _dakshInfoSoftSmsService;
        private readonly IKaleyraSMSService _kaleyraSMSService;
        private readonly IJobService _hangfireService;
        private readonly Serilog.ILogger _logger;
        private readonly IRepositoryWithEvents<NotificationServiceTracker> _notificationServiceTrackerRepo;
        private readonly ITenantInfo _tenantInfo;
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;

        public SMSService(IReadRepository<Domain.Entities.SMSService> smsServiceRepo,
            IRepositoryWithEvents<MasterSMSQuota> quotaRepo,
            IReadRepository<MasterSMSService> masterSMSServiceRepo,
            ITenantInfo currentTenant,
            ITextLocalSmsService textLocalSmsService,
            IDakshInfoSoftSmsService dakshInfoSoftSmsService,
            IKaleyraSMSService kaleyraSMSService,
            IJobService hangfireService,
            Serilog.ILogger logger,
            IRepositoryWithEvents<NotificationServiceTracker> notificationServiceTrackerRepo,
            ITenantInfo tenantInfo,
            ICurrentUser currentUser,
            ILeadRepositoryAsync leadRepositoryAsync)
        {
            _smsServiceRepo = smsServiceRepo;
            _quotaRepo = quotaRepo;
            _masterSMSServiceRepo = masterSMSServiceRepo;
            _currentTenant = currentTenant;
            _dakshInfoSoftSmsService = dakshInfoSoftSmsService;
            _kaleyraSMSService = kaleyraSMSService;
            _textLocalSmsService = textLocalSmsService;
            _hangfireService = hangfireService;
            _logger = logger;
            _notificationServiceTrackerRepo = notificationServiceTrackerRepo;
            _tenantInfo = tenantInfo;
            _currentUser = currentUser;
            _leadRepositoryAsync = leadRepositoryAsync;
        }

        public async Task<string> SendSMSByLRServiceAsync<T>(T entity, MasterTempleteDto? smsTemplate, List<string> phoneNumbers)
        {
            var userId = _currentUser.GetUserId();
            var currentTenant = _currentTenant?.Id;
            var currentTenantQuota = (await _quotaRepo.ListAsync(new GetMasterSMSQuotaByTenantSpec(currentTenant), CancellationToken.None)).FirstOrDefault();
            if (currentTenantQuota != null && currentTenantQuota.SMSQuota > 0)
            {
                try
                {
                    if (entity == null)
                    {
                        return string.Empty;
                    }
                    PropertyInfo[] properties = typeof(T).GetProperties();
                    var entityId = properties?.FirstOrDefault(i => i.Name == "Id")?.GetValue(entity)?.ToString();
                    var masterSMSTemplates = GetMasterSMSTemplates(entity, smsTemplate);
                    var masterSMSService = (await _masterSMSServiceRepo.ListAsync(new GetMasterSMSServiceByIdSpec(smsTemplate.SMSServiceId), CancellationToken.None)).FirstOrDefault();
                    List<NotificationServiceTracker> trackers = new();
                    foreach (var masterTemplate in masterSMSTemplates)
                    {
                        if (masterSMSService != null)
                        {
                            if (masterSMSService != null)
                            {
                                TemplateDto template = new();
                                template.CurrentUserId = userId;
                                template = masterTemplate.Adapt(template);
                                template = masterSMSService.Adapt(template);
                                template.PhoneNumbers = phoneNumbers;
                                template.TenantInfoDto = new()
                                {
                                    Id = _tenantInfo.Id
                                };
                                var jobId = await SendSMSAsync(template);
                                if (!string.IsNullOrEmpty(jobId))
                                {
                                    NotificationServiceTracker tracker = new();
                                    tracker.JobId = jobId;
                                    tracker.EntityId = entityId == null ? Guid.Empty : Guid.Parse(entityId);
                                    tracker.Event = template.Event;
                                    tracker.ScheduledDate = template.ScheduledDate;
                                    trackers.Add(tracker);
                                }
                            }
                        }
                    }
                    foreach (var tracker in trackers)
                    {
                        await _notificationServiceTrackerRepo.AddAsync(tracker, CancellationToken.None);
                    }
                }
                catch (Exception ex) 
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "SMSService -> SendSMSByLRServiceAsync()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                await UpdateQuotaAsync(currentTenantQuota, phoneNumbers);
            }
            return string.Empty;
        }
        public async Task<string> SendSMSByThirdPartyServiceAsync<T>(T entity, SMSTemplateDto? smsTemplate, List<string> phoneNumbers)
        {
            try
            {
                var userId = _currentUser.GetUserId();
                PropertyInfo[] properties = typeof(T).GetProperties();
                var entityId = properties?.FirstOrDefault(i => i.Name == "Id")?.GetValue(entity)?.ToString();
                var newTemplates = GetSMSTemplates(entity, smsTemplate);
                var smsService = (await _smsServiceRepo.ListAsync(new GetSMSServiceByIdSpec(smsTemplate.SMSServiceId), CancellationToken.None)).FirstOrDefault();
                List<NotificationServiceTracker> trackers = new();
                foreach (var newTemplate in newTemplates)
                {
                    if (smsService != null)
                    {
                        TemplateDto template = new();
                        template.CurrentUserId = userId;
                        template = smsTemplate.Adapt(template);
                        template = smsService.Adapt(template);
                        template.PhoneNumbers = phoneNumbers;
                        template.TenantInfoDto = new()
                        {
                            Id = _tenantInfo.Id,
                            ConnectionString = _tenantInfo.ConnectionString,
                            Identifier = _tenantInfo.Identifier,
                            Name = _tenantInfo.Name
                        };
                        var jobId = await SendSMSAsync(template);
                        if (!string.IsNullOrEmpty(jobId))
                        {
                            NotificationServiceTracker tracker = new();
                            tracker.JobId = jobId;
                            tracker.EntityId = entityId == null ? Guid.Empty : Guid.Parse(entityId);
                            tracker.Event = template.Event;
                            tracker.ScheduledDate = template.ScheduledDate;
                            trackers.Add(tracker);
                        }
                    }
                }
                foreach (var tracker in trackers)
                {
                    await _notificationServiceTrackerRepo.AddAsync(tracker, CancellationToken.None);
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "SMSService -> SendSMSByThirdPartyServiceAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return string.Empty;
        }
        private List<TemplateDto> GetMasterSMSTemplates<T>(T entity, MasterTempleteDto template)
        {
            DateTime scheduledDate = DateTime.UtcNow;
            List<TemplateDto> smsTemplateDtos = new List<TemplateDto>();
            TemplateDto smsTemplate = new();
            scheduledDate = typeof(T).FullName == typeof(Todo).FullName
                    ? (DateTime)(typeof(T).GetProperty("ScheduledDateTime").GetValue(entity) ?? DateTime.UtcNow)
                    : typeof(T).FullName == typeof(Lead).FullName
                                ? (DateTime)(typeof(T).GetProperty("ScheduledDate").GetValue(entity) ?? DateTime.UtcNow)
                                : DateTime.UtcNow;
            switch (template.Event)
            {
                case Event.CallbackReminder:
                case Event.ScheduleSiteVisitReminder:
                case Event.ScheduleMeetingReminder:
                case Event.ScheduledTaskReminder:
                    smsTemplate.IsScheduled = true;
                    var minutesBeforeList = new List<int>() { 30, 15 };
                    int i = 0;
                    foreach (var minutes in minutesBeforeList)
                    {
                        if (scheduledDate.AddMinutes(-minutes) < DateTime.UtcNow && i == 0)
                        {
                            smsTemplate = template.Adapt<TemplateDto>();
                            smsTemplate.IsScheduled = false;
                            smsTemplate.Message = smsTemplate.Message?.Replace(NotificationVariables.MinutesBefore, minutes.ToString()) ?? string.Empty;
                            smsTemplateDtos.Add(smsTemplate);
                            i++;
                        }
                        else
                        {
                            smsTemplate = template.Adapt<TemplateDto>();
                            smsTemplate.ScheduledDate = scheduledDate.AddMinutes(-minutes);
                            smsTemplate.IsScheduled = true;
                            smsTemplate.Message = smsTemplate.Message?.Replace(NotificationVariables.MinutesBefore, minutes.ToString()) ?? string.Empty;
                            smsTemplateDtos.Add(smsTemplate);
                        }
                        _logger.Information($"Notification ScheduledTime: {smsTemplate.ScheduledDate}");
                    }
                    break;
                default:
                    smsTemplate = template.Adapt<TemplateDto>();
                    smsTemplateDtos.Add(smsTemplate);
                    break;
            }
            return smsTemplateDtos;
        }
        private List<TemplateDto> GetSMSTemplates<T>(T entity, SMSTemplateDto template)
        {
            DateTime scheduledDate = DateTime.UtcNow;
            List<TemplateDto> smsTemplateDtos = new List<TemplateDto>();
            TemplateDto smsTemplate = new();
            scheduledDate = typeof(T).FullName == typeof(Todo).FullName
                    ? (DateTime)(typeof(T).GetProperty("ScheduledDateTime").GetValue(entity) ?? DateTime.UtcNow)
                    : typeof(T).FullName == typeof(Lead).FullName
                                ? (DateTime)(typeof(T).GetProperty("ScheduledDate").GetValue(entity) ?? DateTime.UtcNow)
                                : DateTime.UtcNow;
            switch (template.Event)
            {
                case Event.CallbackReminder:
                case Event.ScheduleSiteVisitReminder:
                case Event.ScheduleMeetingReminder:
                case Event.ScheduledTaskReminder:
                    smsTemplate.IsScheduled = true;
                    var minutesBeforeList = new List<int>() { 30, 15 };
                    int i = 0;
                    foreach (var minutes in minutesBeforeList)
                    {
                        if (scheduledDate.AddMinutes(-minutes) < DateTime.UtcNow && i == 0)
                        {
                            smsTemplate = template.Adapt<TemplateDto>();
                            smsTemplate.IsScheduled = false;
                            smsTemplate.Message = smsTemplate.Message?.Replace(NotificationVariables.MinutesBefore, minutes.ToString()) ?? string.Empty;
                            smsTemplateDtos.Add(smsTemplate);
                            i++;
                        }
                        else
                        {
                            smsTemplate = template.Adapt<TemplateDto>();
                            smsTemplate.ScheduledDate = scheduledDate.AddMinutes(-minutes);
                            smsTemplate.IsScheduled = true;
                            smsTemplate.Message = smsTemplate.Message?.Replace(NotificationVariables.MinutesBefore, minutes.ToString()) ?? string.Empty;
                            smsTemplateDtos.Add(smsTemplate);
                        }
                        _logger.Information($"Notification ScheduledTime: {smsTemplate.ScheduledDate}");
                    }
                    break;
                default:
                    smsTemplate = template.Adapt<TemplateDto>();
                    smsTemplateDtos.Add(smsTemplate);
                    break;
            }
            return smsTemplateDtos;
        }
        private async Task<string> SendSMSAsync(TemplateDto template)
        {
            string jobId = string.Empty;
            if (template.IsScheduled)
            {
                if (template.SMSServiceProvider == SMSServiceProvider.Textlocal)
                {
                    jobId = _hangfireService.Schedule(() =>
                     (_textLocalSmsService.SendSMSByTextLocalAsync(template)), template.ScheduledDate.ToLocalTime());
                }
                else if (template.SMSServiceProvider == SMSServiceProvider.DakshInfosoft)
                {
                    jobId = _hangfireService.Schedule(() =>
                    (_dakshInfoSoftSmsService.SendSMSByDakshInfoSoftAsync(template)), template.ScheduledDate.ToLocalTime());

                }
                else if (template.SMSServiceProvider == SMSServiceProvider.Kaleyra)
                {
                    jobId = _hangfireService.Schedule(() =>
                    _kaleyraSMSService.SendSMSByKaleyraAsync((template)), template.ScheduledDate.ToLocalTime());
                }
            }
            else
            {
                if (template.SMSServiceProvider == SMSServiceProvider.Textlocal)
                {
                    await _textLocalSmsService.SendSMSByTextLocalAsync(template);
                }
                else if (template.SMSServiceProvider == SMSServiceProvider.DakshInfosoft)
                {
                    await _dakshInfoSoftSmsService.SendSMSByDakshInfoSoftAsync(template);

                }
                else if (template.SMSServiceProvider == SMSServiceProvider.Kaleyra)
                {
                    await _kaleyraSMSService.SendSMSByKaleyraAsync(template);
                }
            }
            return jobId;
        }
        private async Task UpdateQuotaAsync(MasterSMSQuota currentTenantQuota, List<string> phoneNumbers)
        {
            currentTenantQuota.SendCount += phoneNumbers.Count;
            currentTenantQuota.SMSQuota -= (phoneNumbers.Count * 2);
            await _quotaRepo.UpdateAsync(currentTenantQuota, CancellationToken.None);
        }
    }
}

﻿using Lrb.Application.Utils;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Reports.Web
{
    public class V3GetMeetingAndSitevisitReportByUserRequest : IRequest<PagedResponse<LeadAppointmentByUserV3Dto, string>>
    {
        public string? SearchText { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? ToDateForMeetingOrVisit { get; set; }
        public DateTime? FromDateForMeetingOrVisit { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<string>? Projects { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<string>? SubSources { get; set; }
        public UserStatus? UserStatus { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; } = int.MaxValue; 
        public OwnerSelectionType? OwnerSelection { get; set; }
        public List<string>? Countries { get; set; }
        public bool IsWithAssociatedProjects { get; set; } = false;

    }
    public class V3GetMeetingAndSitevisitReportByUserRequestHandler : IRequestHandler<V3GetMeetingAndSitevisitReportByUserRequest, PagedResponse<LeadAppointmentByUserV3Dto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public V3GetMeetingAndSitevisitReportByUserRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<PagedResponse<LeadAppointmentByUserV3Dto, string>> Handle(V3GetMeetingAndSitevisitReportByUserRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            request.FromDateForMeetingOrVisit = request.FromDateForMeetingOrVisit.HasValue ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : null;
            request.ToDateForMeetingOrVisit = request.ToDateForMeetingOrVisit.HasValue ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadAppointmentByUserV3Dto>("LeadratBlack", "GetMeetingAndSitevisitReportByUserV2", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                fromdateformeetingorvisit = request.FromDateForMeetingOrVisit,
                todateformeetingorvisit = request.ToDateForMeetingOrVisit,
                datetype = request.DateType,
                userids = teamUserIds,
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                tenantid = tenantId,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                userstatus = request?.UserStatus ?? 0,
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                ownerselection = request?.OwnerSelection ?? OwnerSelectionType.PrimaryOwner,
                countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                iswithassociatedprojects = request?.IsWithAssociatedProjects

            }, 300))?.ToList();
            var history = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadAppointmentFromHistory>("LeadratBlack", "GetMeetingAndVisitFromHistoryCounts", new
            {
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                from_date = request.FromDate,
                to_date = request.ToDate,
                tenant_id = tenantId,
                user_ids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                userstatus = request?.UserStatus ?? 0,
                fromdateformeetingorvisit = request.FromDateForMeetingOrVisit,
                todateformeetingorvisit = request.ToDateForMeetingOrVisit,
            }, 300))?.ToList();
            List<LeadAppointmentByUserV3Dto> leadAppointments = new();
            foreach (var item in res)
            {
                var matchingHistory = history.FirstOrDefault(i => i.UserId == item.Id);

                if (matchingHistory != null)
                {
                    item.SiteVisitScheduledCountFromHistory = matchingHistory.SiteVisitScheduledCountFromHistory;
                    item.MeetingScheduledCountFromHistory = matchingHistory.MeetingScheduledCountFromHistory;
                    
                    leadAppointments.Add(item);
                }
                else
                {
                    leadAppointments.Add(item);
                }

            }
            return new(leadAppointments, 0);
        }

        //return new (res, 0);
    }
}


﻿using Finbuckle.MultiTenant;
using Hangfire.States;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Models;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.WhatsApp;
using Lrb.Application.Common.WhatsApp.DoubleTick.Services;
using Lrb.Application.Common.WhatsApp.AiSensy.Services;
using Lrb.Application.Common.WhatsApp.Interakt;
using Lrb.Application.Common.WhatsApp.Interakt.Dtos;
using Lrb.Application.Common.WhatsApp.Interakt.Services;
using Lrb.Application.Common.WhatsApp.NoApp;
using Lrb.Application.Common.WhatsApp.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Notifications.Dtos;
using Lrb.Application.WhatsAppCloudApi.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using RestSharp;
using Lrb.Application.Common.WhatsApp.Abracket.Services;

namespace Lrb.Infrastructure.WhatsApp
{
    public class WhatsAppSenderService : IWhatsAppSenderService
    {
        private readonly IRepositoryWithEvents<WhatsAppAPIInfo> _whatsAppApiInfoRepo;
        private readonly IRepositoryWithEvents<WhatsAppSettings> _whatsAppSettingsRepo;
        private readonly IRepositoryWithEvents<GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<WhatsAppConfigurationByServiceProvider> _whatsAppConfigurationByServiceProviderRepo;
        private readonly IInteraktService _interaktService;
        private readonly Serilog.ILogger _logger;
        private readonly IRepositoryWithEvents<WhatsAppCommunication> _whatsAppCommunicationRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<LRWhatsAppApiInfo> _lrWhatsAppApiInfoRepo;
        private readonly IRepositoryWithEvents<WhatsAppTemplateInfo> _whatsAppTemplateInfoRepo;
        private readonly INoAppService _noAppService;
        private readonly IAiSensyService _aiSensyService;
        private readonly IJobService _hangfireService;
        private readonly ITenantInfo _tenantInfo;
        private readonly IRepositoryWithEvents<NotificationTracker> _notificationTrackerRepo;
        private readonly IDoubleTickServices _doubleTickServices;
        private readonly IAbracketService _abracketService;
        public WhatsAppSenderService(IRepositoryWithEvents<WhatsAppAPIInfo> whatsAppApiInfoRepo,
                                    IRepositoryWithEvents<WhatsAppSettings> whatsAppSettingsRepo,
                                    IRepositoryWithEvents<GlobalSettings> globalSettingsRepo,
                                    IRepositoryWithEvents<WhatsAppConfigurationByServiceProvider> whatsAppConfigurationByServiceProviderRepo,
                                    IInteraktService interaktService,
                                    Serilog.ILogger logger,
                                    IRepositoryWithEvents<WhatsAppCommunication> whatsAppCommunicationRepo,
                                    ICurrentUser currentUser,
                                    IRepositoryWithEvents<LRWhatsAppApiInfo> lrWhatsAppApiInfoRepo,
                                    INoAppService noAppService,
                                    IAiSensyService aiSensyService,
                                    IRepositoryWithEvents<WhatsAppTemplateInfo> whatsAppTemplateInfoRepo,
                                    IJobService hangfireService,
                                    ITenantInfo tenantInfo,
                                    IRepositoryWithEvents<NotificationTracker> notificationTrackerRepo,
                                    IDoubleTickServices doubleTickServices,
                                    IAbracketService abracketService)
        {
            _whatsAppApiInfoRepo = whatsAppApiInfoRepo;
            _whatsAppSettingsRepo = whatsAppSettingsRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _whatsAppConfigurationByServiceProviderRepo = whatsAppConfigurationByServiceProviderRepo;
            _interaktService = interaktService;
            _logger = logger;
            _whatsAppCommunicationRepo = whatsAppCommunicationRepo;
            _currentUser = currentUser;
            _lrWhatsAppApiInfoRepo = lrWhatsAppApiInfoRepo;
            _noAppService = noAppService;
            _aiSensyService = aiSensyService;
            _whatsAppTemplateInfoRepo = whatsAppTemplateInfoRepo;
            _hangfireService = hangfireService;
            _tenantInfo = tenantInfo;
            _notificationTrackerRepo = notificationTrackerRepo;
            _doubleTickServices = doubleTickServices;
            _abracketService = abracketService;
        }
        public async Task<Response<string>> SendTextHeaderTemplateAsync(TextOrMediaHeaderTemplateDto templateDto)
        {
            GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec());
            WhatsAppSettings? whatsAppSettings = await _whatsAppSettingsRepo.FirstOrDefaultAsync(new WhatsAppSettingsSpec(), CancellationToken.None);
            List<WhatsAppConfigurationByServiceProvider>? whatsAppConfigurationByServiceProviders = await _whatsAppConfigurationByServiceProviderRepo.ListAsync(new WhatsAppConfigurationByServiceProviderSpec(), CancellationToken.None);
            if ((globalSettings?.IsWhatsAppEnabled ?? false) && (whatsAppSettings?.IsLRServiceUsed ?? false))
            {
                return await SendViaLRService(templateDto, whatsAppConfigurationByServiceProviders);
            }
            else
            {
                return await SendViaConfiguredService(templateDto);
            }
        }
        public async Task<Response<string>> SendCommonTemplateAsync(List<BaseWhatsAppTemplateWithLeadIdDto> baseTemplateWithLeadIdDtos, WhatsAppHeaderTypes whatsAppHeaderTypes, bool? isTestMessage)
        {
            GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec());
            WhatsAppSettings? whatsAppSettings = await _whatsAppSettingsRepo.FirstOrDefaultAsync(new WhatsAppSettingsSpec(), CancellationToken.None);
            List<WhatsAppConfigurationByServiceProvider>? whatsAppConfigurationByServiceProviders = await _whatsAppConfigurationByServiceProviderRepo.ListAsync(new WhatsAppConfigurationByServiceProviderSpec(), CancellationToken.None);
            LRWhatsAppApiInfo? lRWhatsAppApiInfos = (await _lrWhatsAppApiInfoRepo.ListAsync())?.FirstOrDefault(i => i.IsPrimary) ?? null;

            //Adding TenantInfo
            baseTemplateWithLeadIdDtos.ForEach(dto =>
            {
                dto.TenantInfoDto = new()
                {
                    Id = _tenantInfo.Id
                };
                if(dto.CurrentUserId == Guid.Empty)
                {
                    dto.CurrentUserId = _currentUser.GetUserId();
                }
            });

            if ((globalSettings?.IsWhatsAppEnabled ?? false) && (whatsAppSettings?.IsLRServiceUsed ?? false))
            {
                return await SendViaLRService(baseTemplateWithLeadIdDtos, lRWhatsAppApiInfos, whatsAppHeaderTypes, isTestMessage);
            }
            else
            {
                return await SendViaConfiguredService(baseTemplateWithLeadIdDtos, whatsAppHeaderTypes, isTestMessage);
            }
        }


        public async Task<Response<string>> SendViaLRService(List<BaseWhatsAppTemplateWithLeadIdDto> baseTemplateWithLeadIdDtos, LRWhatsAppApiInfo? lRWhatsAppApiInfos, WhatsAppHeaderTypes whatsAppHeaderTypes, bool? isTestMessage)
        {
            WhatsAppAPIInfo? whatsAppApiInfos = await _whatsAppApiInfoRepo.FirstOrDefaultAsync(new WhatsAppApiInfoByTenantSpec(), CancellationToken.None);
            if (whatsAppApiInfos == null)
            {
                return new()
                {
                    Succeeded = false,
                    Message = "WhatsApp Not Configured."
                };
            }
            WhatsAppConfigurationByServiceProvider? waConfiguration = await _whatsAppConfigurationByServiceProviderRepo.FirstOrDefaultAsync(new WhatsAppConfigurationByServiceProviderSpec(lRWhatsAppApiInfos?.ServiceProvider ?? default), CancellationToken.None);
            if (waConfiguration == null)
            {
                return new()
                {
                    Message = "No Default WhatsAppConfigurationByServiceProvider record found. Please make an LRService WhatsApp configuration Default.",
                    Succeeded = false,
                };
            }
            try
            {
                return await SendTemplate(baseTemplateWithLeadIdDtos, waConfiguration, whatsAppHeaderTypes, isTestMessage, whatsAppApiInfos);
            }
            catch (Exception ex)
            {
                _logger.Information($"WhatsAppSenderService -> SendViaConfiguredService -> Exception While Sending Template -> InnerException : {JsonConvert.SerializeObject(ex.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                throw;
            }
        }
        public async Task<Response<string>> SendViaLRService(TextOrMediaHeaderTemplateDto templateDto, List<WhatsAppConfigurationByServiceProvider> whatsAppConfigurationByServiceProviders)
        {
            //WhatsAppConfigurationByServiceProvider? waConfiguration = whatsAppConfigurationByServiceProviders.FirstOrDefault(i => (i.IsLRDefaultService ?? false));
            WhatsAppConfigurationByServiceProvider? waConfiguration = whatsAppConfigurationByServiceProviders.FirstOrDefault();
            if (waConfiguration == null)
            {
                return new()
                {
                    Message = "No Default WhatsAppConfigurationByServiceProvider record found. Please make an LRService WhatsApp configuration Default.",
                    Succeeded = false,
                };
            }

            try
            {
                return await SendTemplate(templateDto, waConfiguration);
            }
            catch (Exception ex)
            {
                _logger.Information($"WhatsAppSenderService -> SendViaConfiguredService -> Exception While Sending Template -> InnerException : {JsonConvert.SerializeObject(ex.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                throw;
            }
        }
        public async Task<Response<string>> SendViaConfiguredService(List<BaseWhatsAppTemplateWithLeadIdDto> baseTemplateWithLeadIdDto, WhatsAppHeaderTypes whatsAppHeaderTypes, bool? IsTestMessage)
        {
            WhatsAppAPIInfo? whatsAppApiInfos = await _whatsAppApiInfoRepo.FirstOrDefaultAsync(new WhatsAppApiInfoByTenantSpec(), CancellationToken.None);
            if (whatsAppApiInfos == null)
            {
                return new()
                {
                    Succeeded = false,
                    Message = "WhatsApp Not Configured."
                };
            }
            WhatsAppConfigurationByServiceProvider? waConfiguration = await _whatsAppConfigurationByServiceProviderRepo.FirstOrDefaultAsync(new WhatsAppConfigurationByServiceProviderSpec(whatsAppApiInfos.ServiceProvider), CancellationToken.None);
            try
            {
                var waConfigurationWithAuth = waConfiguration?.Adapt<WhatsAppConfigurationByServiceProvider>();
                waConfigurationWithAuth.Authorization = whatsAppApiInfos.APIKey;
                return await SendTemplate(baseTemplateWithLeadIdDto, waConfigurationWithAuth, whatsAppHeaderTypes, IsTestMessage, whatsAppApiInfos);
            }
            catch (Exception ex)
            {
                _logger.Information($"WhatsAppSenderService -> SendViaConfiguredService -> Exception While Sending Template -> InnerException : {JsonConvert.SerializeObject(ex.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                throw;
            }
        }
        public async Task<Response<string>> SendViaConfiguredService(TextOrMediaHeaderTemplateDto templateDto)
        {
            WhatsAppAPIInfo? whatsAppApiInfos = await _whatsAppApiInfoRepo.FirstOrDefaultAsync(new WhatsAppApiInfoByTenantSpec(), CancellationToken.None);
            if (whatsAppApiInfos == null)
            {
                return new()
                {
                    Succeeded = false,
                    Message = "WhatsApp Not Configured."
                };
            }
            WhatsAppConfigurationByServiceProvider? waConfiguration = await _whatsAppConfigurationByServiceProviderRepo.FirstOrDefaultAsync(new WhatsAppConfigurationByServiceProviderSpec(whatsAppApiInfos.ServiceProvider), CancellationToken.None);
            try
            {
                return await SendTemplate(templateDto, waConfiguration);
            }
            catch (Exception ex)
            {
                _logger.Information($"WhatsAppSenderService -> SendViaConfiguredService -> Exception While Sending Template -> InnerException : {JsonConvert.SerializeObject(ex.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                throw;
            }
        }
        public async Task<Response<string>> SendTemplate(List<BaseWhatsAppTemplateWithLeadIdDto> baseTemplateWithLeadIdDtos, WhatsAppConfigurationByServiceProvider waConfiguration, WhatsAppHeaderTypes whatsAppHeaderTypes, bool? IsTestMessage, WhatsAppAPIInfo? whatsAppApiInfos)
        {
            var template = await _whatsAppTemplateInfoRepo.FirstOrDefaultAsync(new GetWhatsAppTemplateInfoByNameSpec(baseTemplateWithLeadIdDtos.FirstOrDefault()?.Template?.Name ?? string.Empty), CancellationToken.None);
            RestResponse restResponse = null;
            Guid? currentUserId = _currentUser.GetUserId();
            if(currentUserId == Guid.Empty)
            {
                currentUserId = baseTemplateWithLeadIdDtos?.FirstOrDefault()?.CurrentUserId ?? currentUserId;
            }
            try
            {
                foreach (var dto in baseTemplateWithLeadIdDtos)
                {
                    WhatsAppHeaderTypes waHeaderTypes = template != null ? template.WhatsAppHeaderTypes : dto.WhatsAppHeaderTypes ?? whatsAppHeaderTypes;
                    string? jobId = string.Empty;
                    switch (waConfiguration?.WhatsAppServiceProvider ?? default)
                    {
                        case WhatsAppServiceProvider.Interakt:
                            if ((dto.IsScheduled ?? false) && (dto.ScheduledDateTime != null) && ((dto.ScheduleBeforeMinutes != null) || (dto.ScheduleAfterMinutes != null)))
                            {
                                jobId = _hangfireService.Schedule(() => _interaktService.SendTemplate(dto, waConfiguration, waHeaderTypes, whatsAppApiInfos, template.Adapt<WhatsAppTemplateInfoDto>() ?? new()), dto.ScheduledDateTime.Value);
                                //await _interaktService.SendTemplate(dto, waConfiguration ?? new(), dto.WhatsAppHeaderTypes ?? whatsAppHeaderTypes, whatsAppApiInfos, template?.Adapt<WhatsAppTemplateInfoDto>() ?? new());
                            }
                            else
                            {
                                restResponse = await _interaktService.SendWATemplate(dto, waConfiguration, waHeaderTypes, whatsAppApiInfos, template?.Adapt<WhatsAppTemplateInfoDto>() ?? new());
                            }
                            break;
                        case WhatsAppServiceProvider.NoApp:
                            if ((dto.IsScheduled ?? false) && (dto.ScheduledDateTime != null) && (dto.ScheduleBeforeMinutes != null))
                            {
                                jobId = _hangfireService.Schedule(() => _noAppService.SendTemplate(dto, waConfiguration, dto.WhatsAppHeaderTypes ?? whatsAppHeaderTypes, whatsAppApiInfos, template.Adapt<WhatsAppTemplateInfoDto>() ?? new()), dto.ScheduledDateTime.Value);
                            }
                            else
                            {
                                restResponse = await _noAppService.SendTemplate(dto, waConfiguration, dto.WhatsAppHeaderTypes ?? whatsAppHeaderTypes, whatsAppApiInfos, template.Adapt<WhatsAppTemplateInfoDto>() ?? new());
                            }
                            break;
                        case WhatsAppServiceProvider.DoubleTick:
                            if ((dto.IsScheduled ?? false) && (dto.ScheduledDateTime != null) && (dto.ScheduleBeforeMinutes != null))
                            {
                                jobId = _hangfireService.Schedule(() => _doubleTickServices.SendTemplate(dto, waConfiguration, dto.WhatsAppHeaderTypes ?? whatsAppHeaderTypes, whatsAppApiInfos, template.Adapt<WhatsAppTemplateInfoDto>() ?? new()), dto.ScheduledDateTime.Value);
                            }
                            else
                            {
                                restResponse = await _doubleTickServices.SendTemplate(dto, waConfiguration, dto.WhatsAppHeaderTypes ?? whatsAppHeaderTypes, whatsAppApiInfos, template.Adapt<WhatsAppTemplateInfoDto>() ?? new());
                            }
                            break;
                        case WhatsAppServiceProvider.SmartPing:
                        case WhatsAppServiceProvider.AiSensy:
                            if ((dto.IsScheduled ?? false) && (dto.ScheduledDateTime != null) && (dto.ScheduleBeforeMinutes != null))
                            {
                                jobId = _hangfireService.Schedule(() => _aiSensyService.SendTemplate(dto, waConfiguration, dto.WhatsAppHeaderTypes ?? whatsAppHeaderTypes, whatsAppApiInfos, template.Adapt<WhatsAppTemplateInfoDto>() ?? new()), dto.ScheduledDateTime.Value);
                            }
                            else
                            {
                                restResponse = await _aiSensyService.SendTemplate(dto, waConfiguration, dto.WhatsAppHeaderTypes ?? whatsAppHeaderTypes, whatsAppApiInfos, template.Adapt<WhatsAppTemplateInfoDto>() ?? new());
                            }
                            break;
                        case WhatsAppServiceProvider.Abracket:
                            if ((dto.IsScheduled ?? false) && (dto.ScheduledDateTime != null) && (dto.ScheduleBeforeMinutes != null))
                            {
                                jobId = _hangfireService.Schedule(() => _abracketService.SendTemplate(dto, waConfiguration, dto.WhatsAppHeaderTypes ?? whatsAppHeaderTypes, whatsAppApiInfos, template.Adapt<WhatsAppTemplateInfoDto>() ?? new()), dto.ScheduledDateTime.Value);
                            }
                            else
                            {
                                restResponse = await _abracketService.SendTemplate(dto, waConfiguration, dto.WhatsAppHeaderTypes ?? whatsAppHeaderTypes, whatsAppApiInfos, template.Adapt<WhatsAppTemplateInfoDto>() ?? new());
                            }
                            break;
                    }
                    if ((dto.IsScheduled ?? false) && (dto.ScheduledDateTime != null) && (dto.ScheduleBeforeMinutes != null))
                    {
                        if (dto.LeadId != null && dto.LeadId != Guid.Empty)
                        {
                            NotificationTracker notificationTracker = new()
                            {
                                NotificationId = Guid.Empty,
                                EntityId = dto.LeadId ?? Guid.Empty,
                                Event = dto.Event ?? default,
                                ScheduleTime = dto.ScheduledDateTime ?? DateTime.UtcNow,
                                JobId = jobId, 
                                IsWhatsAppNotification = true,
                            };
                            await _notificationTrackerRepo.AddAsync(notificationTracker);
                        }
                    }
                    if (IsTestMessage ?? false)
                    {
                        //No Logs of Test Templates
                    }
                    else if ((restResponse?.IsSuccessful ?? false))
                    {
                        await _whatsAppCommunicationRepo.AddAsync(new WhatsAppCommunication()
                        {
                            LeadId = dto.LeadId ?? Guid.Empty,
                            UserId = currentUserId ?? Guid.Empty,
                            RequestSent = JsonConvert.SerializeObject(dto),
                            RequestResponse = JsonConvert.SerializeObject(restResponse),
                            TemplateName = dto.Template?.Name ?? string.Empty,
                        });
                    }
                    else
                    {
                        return new()
                        {
                            Succeeded = false,
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new()
                {
                    Succeeded = false,
                };
            }
            return new()
            {
                Succeeded = true,
            };
        }

        public async Task<Response<string>> SendTemplate(TextOrMediaHeaderTemplateDto templateDto, WhatsAppConfigurationByServiceProvider waConfiguration)
        {
            RestResponse restResponse = null;
            switch (waConfiguration?.WhatsAppServiceProvider ?? default)
            {
                case WhatsAppServiceProvider.Interakt:
                    restResponse = await _interaktService.SendTemplate(templateDto, waConfiguration);
                    break;
            }
            if (restResponse?.IsSuccessful ?? false)
            {
                await _whatsAppCommunicationRepo.AddAsync(new WhatsAppCommunication()
                {
                    //LeadId = 
                });
            }
            return new()
            {
                Succeeded = true,
            };
        }

        public async Task<Response<string>> CheckWhatsAppSettings(GlobalSettings? globalSettings, WhatsAppSettings? whatsAppSettings)
        {
            if (globalSettings == null)
            {
                return new()
                {
                    Message = "GlobalSettings Not Found!",
                    Succeeded = false,
                };
            }
            if (!globalSettings.IsWhatsAppEnabled)
            {
                return new()
                {
                    Message = "WhatsApp is not enabled in the GlobalSettings. To enable it, please configure a WhatsApp Account and make it primary.",
                    Succeeded = false,
                };
            }
            if (whatsAppSettings == null)
            {
                return new()
                {
                    Message = "WhatsApp Settings Not Found! Please update WhatsAppSettings.",
                    Succeeded = false,
                };
            }
            if (!whatsAppSettings.IsOtherServiceConfigured)
            {
                return new()
                {
                    Message = "No WhatsApp Account is configured. Please configure an Account.",
                    Succeeded = false,
                };
            }
            return new()
            {
                Succeeded = true,
            };
        }
    }
}
